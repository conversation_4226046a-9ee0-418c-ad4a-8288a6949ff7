import Link from "next/link";

export async function generateMetadata({ params }) {
  const title = 'My External Profiles';
  const description = 'A collection of my external profiles and links to help search engines discover my site.';

  return {
    title: title,
    description: description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/external-links`,
    },
  }
}

export default function CurrentPage() {

  const externalLinks = [
    {
      name: 'GitHub Profile',
      url: 'https://github.com/duan0120',
      desc: 'My open-source projects and contributions.',
    },
    {
      name: 'GitHub Gist',
      url: 'https://gist.github.com/duan0120',
      desc: 'Code snippets and useful scripts I’ve shared.',
    },
    {
      name: 'GitHub Respository',
      url: 'https://github.com/devutils-online/stringartgenerator',
      desc: 'The Ultimate Free Tool for Display Testing and Calibration',
    },
    {
      name: 'Tap <PERSON><PERSON>',
      url: 'https://tap.bio/@string-art-generator',
      desc: 'My Tap.Bio homepage and profile links.',
    },
    {
      name: '<PERSON><PERSON>',
      url: 'https://bento.me/string-art-generator',
      desc: 'My Bento homepage and profile links.',
    },
    {
      name: 'Startup Fame',
      url: 'https://startupfa.me/stringartgenerator',
      desc: 'My Startup Fame homepage and profile links.',
    },
    {
      name: 'folllio',
      url: 'https://folll.io/stringartgenerator',
      desc: 'My folllio homepage and profile links.',
    },
    {
      name: 'Linktree',
      url: 'https://linktr.ee/stringartgenerator',
      desc: 'A quick way to find all my online projects and profiles.',
    },
    {
      name: 'Twitch',
      url: 'https://www.twitch.tv/stringartgenerator/about',
      desc: 'My Twitch channel and profile links.',
    },
    {
      name: 'Onee',
      url: 'https://onee.page/stringartgenerator',
      desc: 'My Onee homepage and profile links.',
    },
    {
      name: 'ollama',
      url: 'https://ollama.com/stringartgenerator',
      desc: 'My ollama homepage and profile links.',
    },
    {
      name: 'bsky.app',
      url: 'https://bsky.app/profile/stringartgenerator.bsky.social',
      desc: 'My bsky.app homepage and profile links.',
    },
    {
      name: 'Cal.com',
      url: 'https://app.cal.com/string-art-generator',
      desc: 'My Cal.com homepage and profile links.',
    },
    {
      name: 'savee',
      url: 'https://savee.com/stringartgenerator/',
      desc: 'My savee homepage and profile links.',
    },
    {
      name: 'gravatar',
      url: 'https://gravatar.com/practicallypurplef0e61e7573',
      desc: 'My gravatar homepage and profile links.',
    },
    {
      name: 'dibiz',
      url: 'https://www.dibiz.com/duanhjlt+art',
      desc: 'My dibiz homepage and profile links.',
    },
    {
      name: 'proko',
      url: 'https://www.proko.com/@stringartgenerator/activity',
      desc: 'My proko homepage and profile links.',
    },
    {
      name: 'behance',
      url: 'https://www.behance.net/bratgenerator',
      desc: 'My behance homepage and profile links.',
    },
    {
      name: 'Product Hunt',
      url: 'https://www.producthunt.com/@duanhjlt',
      desc: 'My Product Hunt homepage and profile links.',
    },
    {
      name: 'String Art Generator | Product Hunt',
      url: 'https://www.producthunt.com/products/string-art-generator-3',
      desc: 'My Product Hunt page for String Art Generator.',
    }
  ];

  return (
    <div className="page-container">
      <div className="mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">My External Profiles</h1>
        <p className="text-lg leading-relaxed">
          This page collects my external profiles on different platforms.
          These profiles may contain links to my website, which can help search engines index and discover my content better.
        </p>
        <h2 className="text-2xl font-bold px-2 py-4">Profiles</h2>
        <ul className="list-disc pl-6 space-y-2 mb-4">
          {externalLinks.map((item, index) => (
            <li key={index}>
              <Link href={item.url} target="_blank" rel="noopener noreferrer" className="text-primary">{item.name}</Link> - {item.desc}
            </li>
          ))}
        </ul>
        <h2 className="text-2xl font-bold px-2 py-4">Notes</h2>
        <p>
          Adding these links here helps Google and other search engines crawl and discover my external profiles.
          Over time, it may strengthen the connection between my site and my public accounts.
        </p>
      </div>
    </div>
  );
}

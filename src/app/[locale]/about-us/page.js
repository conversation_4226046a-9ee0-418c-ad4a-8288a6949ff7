export async function generateMetadata({ params }) {
  const title = 'About String Art Generator - Free Online String Art Creator';
  const description = 'Learn about String Art Generator, the leading free online string art generator. Our team creates powerful tools for converting images into stunning string art patterns with customizable pins and lines.';

  return {
    title: title,
    description: description,
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/about-us`,
    },
  }
}

export default function CurrentPage() {
  return (
    <div className="page-container">
      {/* Hero Section */}
      <div className="section py-16">
        <h1 className="text-4xl font-bold mb-6">About String Art Generator</h1>
        <p className="text-xl w-full mx-auto leading-relaxed">
          We're a passionate team of developers behind the leading <span className="font-bold">free string art generator</span>, dedicated to empowering artists, educators, and DIY enthusiasts with powerful <span className="font-bold">string art creation</span> tools.
        </p>
      </div>

      {/* String Art Generator Story */}
      <div className="section">
        <div className="w-full mx-auto">
          <p className="text-lg leading-relaxed mb-6">
            String Art Generator was launched in 2024 by a team of passionate developers and artists inspired by the timeless beauty of <span className="font-bold">string art</span>. Starting as an innovative project, our <span className="font-bold">free string art generator online</span> has grown into a global tool loved by millions for creating stunning <span className="font-bold">string art patterns</span>.
          </p>
          <p className="text-lg leading-relaxed mb-6">
            Our goal is to deliver a seamless, intuitive experience with the <span className="font-bold">string art generator</span>, allowing users to upload images, customize settings with 288 pins and 4000 lines, and generate downloadable patterns for crafting. We're committed to making <span className="font-bold">string art creation</span> accessible to everyone without barriers.
          </p>
          <p className="text-lg leading-relaxed mb-6">
            What sets String Art Generator apart is our focus on user freedom and quality. Our <span className="font-bold">string art generator free</span> tool is completely free, requires no registration, and provides high-quality TXT and SVG outputs, ensuring a pure <span className="font-bold">string art</span> creative experience.
          </p>
          <p className="text-lg leading-relaxed">
            As digital art evolves, we stay dedicated to enhancing our <span className="font-bold">string art generator online</span>, keeping it aligned with the latest technology and user needs. Our team works tirelessly to refine features, ensuring artists, educators, and DIY enthusiasts can create and share <span className="font-bold">string art patterns</span> effortlessly.
          </p>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="section mb-12">
        <div className="w-full mx-auto flex justify-between gap-8">
          <div className="bg-foreground/5 rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
            <p className="text-lg leading-relaxed">
              To empower artists, educators, and DIY enthusiasts with a <span className="font-bold">free string art generator</span> for creating stunning <span className="font-bold">string art patterns</span>, prioritizing ease, quality, and accessibility in <span className="font-bold">string art creation</span>.
            </p>
          </div>
          <div className="bg-foreground/5 rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-4">Our Vision</h2>
            <p className="text-lg leading-relaxed">
              To inspire a global community of creators with our <span className="font-bold">string art generator online</span>, enabling users to craft and share beautiful <span className="font-bold">string art</span> that bridges technology and traditional craftsmanship.
            </p>
          </div>
        </div>
      </div>

      {/* Our Impact */}
      <div className="section mb-12">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-4xl font-bold text-blue-600 mb-2">500K+</div>
            <div className="text-lg text-gray-600">String Art Patterns</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-green-600 mb-2">150+</div>
            <div className="text-lg text-gray-600">Countries</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-purple-600 mb-2">99.9%</div>
            <div className="text-lg text-gray-600">Uptime</div>
          </div>
          <div>
            <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-lg text-gray-600">Free Access</div>
          </div>
        </div>
      </div>

      {/* Contact Us */}
      <div className="section">
        <div className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Contact Us</h2>
        </div>
        <p className="text-lg leading-relaxed">
          Have questions about our <span className="font-bold">String Art Generator</span> or need help with <span className="font-bold">string art creation</span>? Reach out at <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a>.
        </p>
        <p className="text-lg leading-relaxed mt-2">
          You can also find me on other platforms where I share projects and ideas.
          <a href="/external-links" className="text-blue-600">See all my profiles here.</a>
        </p>
      </div>
    </div>
  );
}
